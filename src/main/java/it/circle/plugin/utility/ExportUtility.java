package it.circle.plugin.utility;


import it.circle.plugin.model.FieldMetadata;
import it.circle.plugin.service.FieldMetadataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ExportUtility {

    private static FieldMetadataService fieldMetadataService;


    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter FILENAME_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");

    private ExportUtility() {
        // Utility class - prevent instantiation
    }

    /**
     * Generate filename for export with timestamp
     */
    public static String generateFileName(String entityType, String instanceId, String owner) {
        String timestamp = LocalDateTime.now().format(FILENAME_DATE_FORMATTER);
        String ownerPrefix = owner != null ? owner.split("@")[0] : "unknown";
        String entityLower = entityType.toLowerCase();
        return String.format("%s_export_%s_%s_%s", entityLower, instanceId, ownerPrefix, timestamp);
    }

    /**
     * Save Excel file to specified path
     */
    public static String saveExcelFile(ByteArrayOutputStream excelFile, String fileName, String exportPath) {
        try {
            String fullPath = exportPath + "/" + fileName + ".xlsx";

            // Create directory if it doesn't exist
            java.nio.file.Files.createDirectories(java.nio.file.Paths.get(exportPath));

            // Save file
            try (FileOutputStream fileOut = new FileOutputStream(fullPath)) {
                excelFile.writeTo(fileOut);
                log.info("Excel file saved to: {}", fullPath);
            }

            return fullPath;

        } catch (IOException e) {
            log.error("Error saving Excel file: {}", fileName, e);
            throw new RuntimeException("Failed to save Excel file", e);
        }
    }

    /**
     * Format LocalDateTime for export
     */
    public static String formatDateForExport(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DATE_FORMATTER) : null;
    }

    /**
     * Format Date for export (converts to LocalDateTime first)
     */
    public static String formatDateForExport(Date date) {
        if (date == null) {
            return null;
        }
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return formatDateForExport(localDateTime);
    }

    /**
     * Log export summary
     */
    public static void logExportSummary(String entityType, String instanceId, String owner,
                                      int recordCount, String fileName) {
        log.info("Export Summary - Entity: {}, Instance: {}, Owner: {}, Records: {}, File: {}",
                entityType, instanceId, owner, recordCount, fileName);
    }


    /**
     * Format field value for export (handle dates, collections, etc.)
     */
    private static Object formatFieldValue(Object value) {
        if (value == null) {
            return null;
        }

        // Handle dates
        if (value instanceof java.time.LocalDateTime) {
            return formatDateForExport((java.time.LocalDateTime) value);
        } else if (value instanceof java.util.Date) {
            return formatDateForExport((java.util.Date) value);
        }

        // Handle collections - return first element or comma-separated string
        if (value instanceof java.util.Collection<?> collection) {
            if (collection.isEmpty()) {
                return null;
            }

            // For simple collections, join with comma
            if (collection.size() == 1) {
                return formatFieldValue(collection.iterator().next());
            } else {
                return collection.stream()
                    .map(Object::toString)
                    .collect(Collectors.joining(", "));
            }
        }

        // Handle enums
        if (value instanceof Enum<?>) {
            return value.toString();
        }

        return value;
    }


}
